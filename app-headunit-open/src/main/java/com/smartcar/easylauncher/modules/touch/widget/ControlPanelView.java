package com.smartcar.easylauncher.modules.touch.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;

import com.smartcar.easylauncher.R;
import com.smartcar.easylauncher.shared.utils.ui.DensityUtils;

import com.smartcar.easylauncher.shared.utils.ui.ScreenUtils;
/**
 * 控制面板视图类，继承自FrameLayout，主要用于显示一个可展开和收起的控制面板。
 */
public class ControlPanelView extends FrameLayout {

    // 控件状态相关字段

    /**
     * 开关状态，表示面板是否打开
     */
    private boolean isOpen = false;

    /**
     * 面板的当前半径，开关动画时会用到
     */
    private int mPanelRadius;

    // 布局和尺寸相关字段

    /**
     * 半圆的原心位置到子控件之间的距离
     */
    private int mRadius;

    /**
     * 子控件的半径
     */
    private int mChildRadius;

    /**
     * 按钮的中心点X坐标
     */
    private int mButtonCenterX;

    /**
     * 按钮的中心点Y坐标
     */
    private int mButtonCenterY;

    // 动画相关字段

    /**
     * 打开面板的动画
     */
    private ValueAnimator mOpenAnimator;

    /**
     * 关闭面板的动画
     */
    private ValueAnimator mOffAnimator;

    // 自定义属性相关字段

    /**
     * 第一个按钮的角度
     */
    private int mStartAngle = -90;

    /**
     * 面板的宽度
     */
    private int mWidth;

    /**
     * 面板的高度
     */
    private int mHeight;

    /**
     * 面板吸附边：0=左，1=右，2=上，3=下
     */
    private int attachSide = 0;

    /**
     * 兼容旧逻辑：是否在左边
     */
    private boolean isLeft = false;

    /**
     * 相对吸附边的偏移量
     */
    private int mOffset;
    // 缓存屏幕尺寸与边距，减少拖动时计算
    private int cachedScreenW = -1;
    private int cachedScreenH = -1;
    private int followMarginPx = -1;

    // 回调接口相关字段

    /**
     * 面板开关状态变化的监听器
     */
    private OnTogglePanelListener mTogglePanelListener;

    /**
     * 面板尺寸变化时的回调
     */
    private OnPanelSizeChangeCallback mOnPanelSizeChangeCallback;

    // 构造函数

    /**
     * 用于在代码中创建ControlPanelView对象的构造函数
     *
     * @param context 上下文环境
     */
    public ControlPanelView(Context context) {
        this(context, null);
    }

    /**
     * 用于在XML布局文件中使用该自定义控件的构造函数
     *
     * @param context 上下文环境
     * @param attrs   属性集合
     */
    public ControlPanelView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    /**
     * 用于在XML布局文件中使用该自定义控件，并且还可以在布局文件中设置style样式的构造函数
     *
     * @param context         上下文环境
     * @param attrs           属性集合
     * @param defStyleAttr    默认的样式属性
     */
    public ControlPanelView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    // 初始化方法

    /**
     * 初始化一些变量和设置
     *
     * @param context 上下文环境
     * @param attrs   属性集合
     */
    private void init(Context context, AttributeSet attrs) {
        setWillNotDraw(false); // 允许绘制背景
        setClipChildren(false);
        setClipToPadding(false);
        this.mOffset = 0;
        setStartAngle();
    }

    /**
     * 设置开始角度
     * 根据吸附边来确定按钮的开始角度
     */
    private void setStartAngle() {
        switch (attachSide) {
            case 0: // 左
                mStartAngle = -90;
                break;
            case 1: // 右
                mStartAngle = 90;
                break;
            case 2: // 上（向下展开，0°→180°）
                mStartAngle = 0;
                break;
            case 3: // 下（向上展开，180°→360°）
                mStartAngle = 180;
                break;
            default:
                mStartAngle = isLeft ? -90 : 90;
        }
    }

    // 当控件大小发生改变时，这个方法会被调用
    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        this.mWidth = w;
        this.mHeight = h;
        if (mOnPanelSizeChangeCallback != null) {
            mOnPanelSizeChangeCallback.onPanelSizeChange(mWidth, mHeight);
        }
    }

    // 测量控件大小
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);
        int count = getChildCount();
        // 重新计算所有子View的最大半径，避免只取最后一个的尺寸
        int maxChildRadius = 0;
        for (int i = 0; i < count; i++) {
            View childView = getChildAt(i);
            measureChild(childView, widthMeasureSpec, heightMeasureSpec);
            int r = Math.max(childView.getMeasuredWidth(), childView.getMeasuredHeight()) / 2;
            if (r > maxChildRadius) maxChildRadius = r;
        }
        mChildRadius = maxChildRadius;
        // 计算半圆的圆心到子控件圆心的距离（半径），预留边距 16dp（使用系统DisplayMetrics，避免适配放大）
        int edgeMargin = DensityUtils.dp2pxSystem(16);
        // 统一半径：按较大边的一半为基准，保证上下贴边半径足够，避免子项重叠
        mRadius = Math.max(widthSize, heightSize) / 2 - (mChildRadius * 2) - edgeMargin;
        if (mRadius < 1) { mRadius = 1; }
        // 根据吸附边计算按钮圆心
        switch (attachSide) {
            case 0: // 左
                mButtonCenterX = mOffset;
                mButtonCenterY = heightSize / 2;
                break;
            case 1: // 右
                mButtonCenterX = widthSize - mOffset;
                mButtonCenterY = heightSize / 2;
                break;
            case 2: // 上
                mButtonCenterX = widthSize / 2;
                mButtonCenterY = mOffset;
                break;
            case 3: // 下
                mButtonCenterX = widthSize / 2;
                mButtonCenterY = heightSize - mOffset;
                break;
            default:
                if (isLeft) {
                    mButtonCenterX = mOffset;
                    mButtonCenterY = heightSize / 2;
                } else {
                    mButtonCenterX = widthSize - mOffset;
                    mButtonCenterY = heightSize / 2;
                }
        }
        setMeasuredDimension(widthSize, heightSize);
    }

    // 当布局加载完成后，这个方法会被调用
    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        if (isInEditMode()) {
            return;
        }
        int childCount = getChildCount();
        //一开始，是关闭状态，子View全部隐藏
        for (int i = 0; i < childCount; i++) {
            getChildAt(i).setVisibility(View.GONE);
        }
    }

    // 布局子控件
    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        int count = getChildCount();
        if (count <= 0) {
            return;
        }
        //每份子控件应该占用的角度（count>=1安全）
        int childAngle = 180 / count;
        //每个子View之间的间隔（count==1时为0，避免除0）
        int interval = (count > 1) ? (180 / count) / (count - 1) : 0;
        for (int i = 0; i < count; i++) {
            //计算出每个子控件的位置
            float[] point;
            // 不同吸附边的排序方向
            if (attachSide == 0) { // 左，从上到下
                point = getCoordinatePoint(mPanelRadius, mStartAngle + ((i * childAngle) + (i * interval)));
            } else if (attachSide == 1) { // 右，从上到下（镜像排序）
                int fixPosition = count - 1 - i;
                point = getCoordinatePoint(mPanelRadius, mStartAngle + ((fixPosition * childAngle) + (fixPosition * interval)));
            } else if (attachSide == 2) { // 上，从左到右
                point = getCoordinatePoint(mPanelRadius, mStartAngle + ((i * childAngle) + (i * interval)));
            } else if (attachSide == 3) { // 下，从右到左（镜像排序）
                int fixPosition = count - 1 - i;
                point = getCoordinatePoint(mPanelRadius, mStartAngle + ((fixPosition * childAngle) + (fixPosition * interval)));
            } else {
                if (!isLeft) {
                    int fixPosition = count - 1 - i;
                    point = getCoordinatePoint(mPanelRadius, mStartAngle + ((fixPosition * childAngle) + (fixPosition * interval)));
                } else {
                    point = getCoordinatePoint(mPanelRadius, mStartAngle + ((i * childAngle) + (i * interval)));
                }
            }
            View childView = getChildAt(i);
            int childViewWidth = childView.getMeasuredWidth();
            int childViewHeight = childView.getMeasuredHeight();
            int halfWidth = childViewWidth / 2;
            int halfHeight = childViewHeight / 2;
            //布局子控件
            int pointX = (int) point[0];
            int pointY = (int) point[1];
            switch (attachSide) {
                case 0: // 左，右对齐到圆弧点
                    childView.layout(pointX, pointY - halfHeight, pointX + childViewWidth, pointY + halfHeight);
                    break;
                case 1: // 右，左对齐到圆弧点
                    childView.layout(pointX - childViewWidth, pointY - halfHeight, pointX, pointY + halfHeight);
                    break;
                case 2: // 上，下对齐到圆弧点
                    childView.layout(pointX - halfWidth, pointY, pointX + halfWidth, pointY + childViewHeight);
                    break;
                case 3: // 下，上对齐到圆弧点
                    childView.layout(pointX - halfWidth, pointY - childViewHeight, pointX + halfWidth, pointY);
                    break;
                default:
                    if (isLeft) {
                        childView.layout(pointX, pointY - halfHeight, pointX + childViewWidth, pointY + halfHeight);
                    } else {
                        childView.layout(pointX - childViewWidth, (pointY - halfHeight), (pointX), (pointY + halfHeight));
                    }
            }
        }
    }

    /**
     * 依圆心坐标，半径，扇形角度，计算出扇形终射线与圆弧交叉点的xy坐标
     *
     * @param angle 每个子控件和面板圆心的夹角
     */
    public float[] getCoordinatePoint(int panelRadius, float angle) {
        float[] point = new float[2];
        //Math类的三角函数是弧度制，所以要将角度转换为弧度才能进行计算
        double arcAngle = Math.toRadians(angle);
        //求子控件的X坐标，邻边 / 斜边，斜边的值刚好就是半径，cos值乘以斜边，就能求出邻边，而这个邻边的长度，就是点的x坐标
        point[0] = (float) (mButtonCenterX + Math.cos(arcAngle) * panelRadius);
        //求子控件的Y坐标，对边 / 斜边，斜边的值刚好就是半径，sin值乘以斜边，就能求出对边，而这个对边的长度，就是点的y坐标
        point[1] = (float) (mButtonCenterY + Math.sin(arcAngle) * panelRadius);
        return point;
    }

    public void offNow() {
        if (isOpen) {
            startOffAnimation();
            isOpen = !isOpen;
        }
    }

    public void openNow() {
        if (!isOpen) {
            startOpenAnimation();
            isOpen = !isOpen;
        }
    }

    public boolean isOpen() {
        return isOpen;
    }

    public boolean isAnimationRunning() {
        if (mOpenAnimator != null && mOpenAnimator.isRunning()) {
            return true;
        }
        return mOffAnimator != null && mOffAnimator.isRunning();
    }

    /**
     * 打开动画
     */
    private void startOpenAnimation() {
        // 如果打开动画已经在运行，就直接返回，不再执行后续代码
        if (mOpenAnimator != null && mOpenAnimator.isRunning()) {
            return;
        }
        // 创建一个值动画，值从0变化到mRadius
        mOpenAnimator = ValueAnimator.ofInt(0, mRadius);
        // 设置动画的持续时间为250毫秒
        mOpenAnimator.setDuration(250);
        // 设置动画的插值器，这里使用了一个减速插值器
        mOpenAnimator.setInterpolator(AnimationUtils.loadInterpolator(getContext(), R.anim.decelerate_interpolator_more));
        // 设置动画的更新监听器
        mOpenAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                // 获取当前动画的值
                int cValue = (int) valueAnimator.getAnimatedValue();
                // 计算当前的透明度（避免除0）
                float alpha = mRadius > 0 ? (cValue * 1f / mRadius) : 1f;
                // 获取子控件的数量
                int childCount = getChildCount();
                // 遍历所有的子控件，设置它们的透明度
                for (int i = 0; i < childCount; i++) {
                    getChildAt(i).setAlpha(alpha);
                }
                // 更新面板半径
                mPanelRadius = cValue;
                // 请求重新布局
                requestLayout();
            }
        });
        // 设置动画的监听器
        mOpenAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                // 当动画开始时，将所有的子控件设置为可见
                int childCount = getChildCount();
                for (int i = 0; i < childCount; i++) {
                    getChildAt(i).setVisibility(View.VISIBLE);
                }
                // 如果设置了面板切换监听器，那么通知监听器面板已经打开
                if (mTogglePanelListener != null) {
                    mTogglePanelListener.onToggleChange(true);
                }
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                // 当动画结束时，将mOpenAnimator设置为null
                mOpenAnimator = null;
            }
        });
        // 开始动画
        mOpenAnimator.start();
    }

    /**
     * 关闭动画
     */
    private void startOffAnimation() {
        // 如果关闭动画已经在运行，就直接返回，不再执行后续代码
        if (mOffAnimator != null && mOffAnimator.isRunning()) {
            return;
        }
        // 创建一个值动画，值从mRadius变化到0
        mOffAnimator = ValueAnimator.ofInt(mRadius, 0);
        // 设置动画的持续时间为200毫秒
        mOffAnimator.setDuration(200);
        // 设置动画的插值器，这里使用了一个减速插值器
        mOffAnimator.setInterpolator(AnimationUtils.loadInterpolator(getContext(), R.anim.decelerate_interpolator_more));
        // 设置动画的更新监听器
        mOffAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                // 获取当前动画的值
                int cValue = (int) valueAnimator.getAnimatedValue();
                // 计算当前的透明度（避免除0）
                float alpha = mRadius > 0 ? (cValue * 1f / mRadius) : 1f;
                // 获取子控件的数量
                int childCount = getChildCount();
                // 遍历所有的子控件，设置它们的透明度
                for (int i = 0; i < childCount; i++) {
                    getChildAt(i).setAlpha(alpha);
                }
                // 更新面板半径
                mPanelRadius = cValue;
                // 请求重新布局
                requestLayout();
            }
        });
        // 设置动画的监听器
        mOffAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 当动画结束时，将所有的子控件设置为不可见
                int childCount = getChildCount();
                for (int i = 0; i < childCount; i++) {
                    getChildAt(i).setVisibility(View.GONE);
                }
                // 如果设置了面板切换监听器，那么通知监听器面板已经关闭
                if (mTogglePanelListener != null) {
                    mTogglePanelListener.onToggleChange(false);
                }
                // 将mOffAnimator设置为null
                mOffAnimator = null;
            }
        });
        // 开始动画
        mOffAnimator.start();
    }

    public void setOrientation(boolean isLeft) {
        // 兼容旧接口：仅左右
        int side = isLeft ? 0 : 1;
        setAttachSide(side);
    }

    /**
     * 修正跟随位置：根据按钮位置自动判定最近边，并返回面板容器应放置的位置
     * 内部使用子项半径作为锚点半径（兼容旧逻辑）
     */
    public int[] followButtonPosition(int x, int y) {
        return followButtonPosition(x, y, mChildRadius);
    }

    /**
     * 修正跟随位置（带按钮半径）：用实际按钮半径对齐，避免“不是在中心”的错位
     */
    public int[] followButtonPosition(int x, int y, int buttonRadius) {
        int screenW = ScreenUtils.getWindowWidth(getContext());
        int screenH = ScreenUtils.getWindowHeigh(getContext());
        int margin = DensityUtils.dp2px(getContext(), 5);

        // 计算到四边的距离（扣除margin）
        int distLeft = Math.max(0, x - margin);
        int distRight = Math.max(0, screenW - x - margin);
        int distTop = Math.max(0, y - margin);
        int distBottom = Math.max(0, screenH - y - margin);

        // 选择最近边
        int min = distLeft;
        int side = 0; // 0左 1右 2上 3下
        if (distRight < min) { min = distRight; side = 1; }
        if (distTop < min) { min = distTop; side = 2; }
        if (distBottom < min) { min = distBottom; side = 3; }
        setAttachSide(side);

        // 计算容器位置（用按钮半径对齐中心）
        int[] result = new int[2];
        switch (side) {
            case 0: // 左
                result[0] = x;
                result[1] = y - (mHeight / 2) + buttonRadius;
                break;
            case 1: // 右
                result[0] = x - mWidth + (buttonRadius * 2);
                result[1] = y - (mHeight / 2) + buttonRadius;
                break;
            case 2: // 上
                result[0] = x - (mWidth / 2) + buttonRadius;
                result[1] = y;
                break;
            case 3: // 下
                result[0] = x - (mWidth / 2) + buttonRadius;
                result[1] = y - mHeight + (buttonRadius * 2);
                break;
        }
        return result;
    }
    /**
     * 设置吸附边（0=左，1=右，2=上，3=下）
     */
    public void setAttachSide(int side) {
        if (this.attachSide != side) {
            this.attachSide = side;
            // 兼容旧逻辑
            this.isLeft = (side == 0);
            setStartAngle();
            requestLayout();
        }
    }

    /**
     * 获取当前吸附边
     */
    public int getAttachSide() {
        return attachSide;
    }

    public interface OnTogglePanelListener {
        /**
         * 当切换开关状态时回调
         *
         * @param isOpen 当前是否是打开
         */
        void onToggleChange(boolean isOpen);
    }

    public interface OnPanelSizeChangeCallback {
        // 当面板大小改变时回调
        void onPanelSizeChange(int newWidth, int newHeight);
    }

    public void setOnPanelSizeChangeCallback(OnPanelSizeChangeCallback onPanelSizeChangeCallback) {
        // 设置面板大小改变的回调
        this.mOnPanelSizeChangeCallback = onPanelSizeChangeCallback;
    }

    public void setOnTogglePanelListener(OnTogglePanelListener togglePanelListener) {
        // 设置切换面板监听器
        this.mTogglePanelListener = togglePanelListener;
    }



    public static DisplayMetrics getDisplayMetrics(Context context) {
        // 获取屏幕的显示指标
        return context.getResources().getDisplayMetrics();
    }
}